"""
FastAPI application for English Test System
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from typing import List, Optional
import json
import time
from datetime import datetime

from models import (
    Test, Question, TestSubmission, TestResult,
    UserAnswer, TestInfo, QuestionOption, Passage
)
from database import db

app = FastAPI(
    title="English Test System",
    description="A web application for taking English proficiency tests",
    version="1.0.0"
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main HTML page"""
    try:
        with open("static/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Welcome to English Test System</h1><p>Frontend not found</p>")

@app.get("/api/tests", response_model=List[TestInfo])
async def get_tests():
    """Get information about all available tests"""
    tests = db.get_all_tests_info()
    if not tests:
        raise HTTPException(status_code=404, detail="No tests available")
    return tests

@app.get("/api/test/{test_id}", response_model=TestInfo)
async def get_test_info(test_id: str):
    """Get information about a specific test"""
    test_info = db.get_test_info(test_id)
    if not test_info:
        raise HTTPException(status_code=404, detail="Test not found")
    return test_info

@app.get("/api/test/{test_id}/questions", response_model=List[Question])
async def get_test_questions(test_id: str, start: Optional[int] = None, end: Optional[int] = None):
    """Get questions for a specific test"""
    test = db.get_test(test_id)
    if not test:
        raise HTTPException(status_code=404, detail="Test not found")
    
    if start is not None and end is not None:
        questions = db.get_questions_range(test_id, start, end)
    else:
        questions = test.questions
    
    return questions

@app.get("/api/test/{test_id}/question/{question_number}", response_model=Question)
async def get_question(test_id: str, question_number: int):
    """Get a specific question from a test"""
    question = db.get_question(test_id, question_number)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    return question

@app.post("/api/test/{test_id}/submit", response_model=TestResult)
async def submit_test(test_id: str, submission: TestSubmission):
    """Submit test answers and get results"""
    test = db.get_test(test_id)
    if not test:
        raise HTTPException(status_code=404, detail="Test not found")
    
    # Validate that the test_id matches
    if submission.test_id != test_id:
        raise HTTPException(status_code=400, detail="Test ID mismatch")
    
    # Calculate results
    total_questions = len(test.questions)
    answered_questions = len(submission.answers)
    
    # For now, we don't have correct answers, so we'll just return the submission
    # In a real application, you would compare with correct answers
    result = TestResult(
        test_id=test_id,
        total_questions=total_questions,
        answered_questions=answered_questions,
        answers=submission.answers,
        score=None,  # Would calculate based on correct answers
        percentage=None  # Would calculate based on score
    )
    
    return result

@app.get("/api/test/{test_id}/passages", response_model=List[Passage])
async def get_test_passages(test_id: str):
    """Get reading passages for a test"""
    test = db.get_test(test_id)
    if not test:
        raise HTTPException(status_code=404, detail="Test not found")

    return test.passages

@app.get("/api/test/{test_id}/passage/{passage_id}", response_model=Passage)
async def get_passage(test_id: str, passage_id: str):
    """Get a specific passage from a test"""
    passage = db.get_passage(test_id, passage_id)
    if not passage:
        raise HTTPException(status_code=404, detail="Passage not found")
    return passage

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "tests_loaded": len(db.tests)
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
