#!/usr/bin/env python3
"""
Create sample Part 6 questions for the English test
"""

import json

def create_part6_questions():
    """Create sample Part 6 questions with paragraph context"""
    
    # Sample paragraph for Part 6 questions
    paragraph_text = """
Dear Valued Customer,

We are pleased to inform you that your recent order has been processed and is ready for shipment. Our team has worked diligently to ensure that all items meet our high quality standards before dispatch.

Your order will be shipped within the next 24 hours via our express delivery service. You can expect to receive your package within 3-5 business days. A tracking number will be provided once the shipment has been dispatched.

If you have any questions or concerns regarding your order, please do not hesitate to contact our customer service team. We are available Monday through Friday from 9:00 AM to 6:00 PM.

Thank you for choosing our company for your shopping needs. We look forward to serving you again in the future.

Sincerely,
Customer Service Team
"""

    part6_questions = [
        {
            "number": 141,
            "question": "We are pleased to inform you that your recent order has been processed and is ready for -------.",
            "options": [
                {"letter": "A", "text": "shipment"},
                {"letter": "B", "text": "shipping"},
                {"letter": "C", "text": "shipped"},
                {"letter": "D", "text": "ship"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text,
            "blank_positions": [1]
        },
        {
            "number": 142,
            "question": "Our team has worked ------- to ensure that all items meet our high quality standards before dispatch.",
            "options": [
                {"letter": "A", "text": "diligent"},
                {"letter": "B", "text": "diligently"},
                {"letter": "C", "text": "diligence"},
                {"letter": "D", "text": "more diligent"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text,
            "blank_positions": [2]
        },
        {
            "number": 143,
            "question": "Your order will be shipped within the next 24 hours ------- our express delivery service.",
            "options": [
                {"letter": "A", "text": "by"},
                {"letter": "B", "text": "via"},
                {"letter": "C", "text": "through"},
                {"letter": "D", "text": "with"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text,
            "blank_positions": [3]
        },
        {
            "number": 144,
            "question": "A tracking number will be provided ------- the shipment has been dispatched.",
            "options": [
                {"letter": "A", "text": "before"},
                {"letter": "B", "text": "once"},
                {"letter": "C", "text": "while"},
                {"letter": "D", "text": "until"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text,
            "blank_positions": [4]
        }
    ]
    
    # Second paragraph for more Part 6 questions
    paragraph_text_2 = """
Attention All Employees,

Due to scheduled maintenance work on our computer systems, there will be a temporary disruption to our network services this weekend. The maintenance is necessary to upgrade our security protocols and improve system performance.

The work will begin on Saturday at 6:00 PM and is expected to be completed by Sunday at 8:00 AM. During this time, access to email, shared drives, and other network resources will be unavailable.

We apologize for any inconvenience this may cause and appreciate your understanding. If you have any urgent matters that require immediate attention, please contact the IT helpdesk before Friday at 5:00 PM.

Best regards,
IT Department
"""

    part6_questions_2 = [
        {
            "number": 145,
            "question": "Due to scheduled maintenance work on our computer systems, there will be a temporary ------- to our network services this weekend.",
            "options": [
                {"letter": "A", "text": "disruption"},
                {"letter": "B", "text": "disrupting"},
                {"letter": "C", "text": "disrupt"},
                {"letter": "D", "text": "disrupted"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text_2,
            "blank_positions": [1]
        },
        {
            "number": 146,
            "question": "The maintenance is necessary to upgrade our security protocols and ------- system performance.",
            "options": [
                {"letter": "A", "text": "improve"},
                {"letter": "B", "text": "improving"},
                {"letter": "C", "text": "improvement"},
                {"letter": "D", "text": "improved"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text_2,
            "blank_positions": [2]
        },
        {
            "number": 147,
            "question": "The work will begin on Saturday at 6:00 PM and is expected to be ------- by Sunday at 8:00 AM.",
            "options": [
                {"letter": "A", "text": "complete"},
                {"letter": "B", "text": "completing"},
                {"letter": "C", "text": "completed"},
                {"letter": "D", "text": "completion"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text_2,
            "blank_positions": [3]
        },
        {
            "number": 148,
            "question": "------- this time, access to email, shared drives, and other network resources will be unavailable.",
            "options": [
                {"letter": "A", "text": "During"},
                {"letter": "B", "text": "While"},
                {"letter": "C", "text": "Throughout"},
                {"letter": "D", "text": "Within"}
            ],
            "correct_answer": None,
            "question_type": "part6",
            "paragraph_text": paragraph_text_2,
            "blank_positions": [4]
        }
    ]
    
    return part6_questions + part6_questions_2

def add_part6_to_enhanced_data():
    """Add Part 6 questions to the enhanced test data"""
    part6_questions = create_part6_questions()
    
    for test_file in ['test-1_enhanced.json', 'test-2_enhanced.json']:
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Find where to insert Part 6 questions (between Part 5 and Part 7)
            part5_questions = [q for q in data['questions'] if q.get('question_type') == 'part5']
            part7_questions = [q for q in data['questions'] if q.get('question_type') == 'part7']
            
            # Create new questions list with Part 6 inserted
            new_questions = part5_questions + part6_questions + part7_questions
            
            # Update the data
            data['questions'] = new_questions
            
            # Save back to file
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"Added {len(part6_questions)} Part 6 questions to {test_file}")
            
            # Show question type distribution
            type_counts = {}
            for q in data['questions']:
                qtype = q.get('question_type', 'unknown')
                type_counts[qtype] = type_counts.get(qtype, 0) + 1
            
            print(f"Updated question types: {type_counts}")
            
        except Exception as e:
            print(f"Error processing {test_file}: {e}")

if __name__ == "__main__":
    add_part6_to_enhanced_data()
