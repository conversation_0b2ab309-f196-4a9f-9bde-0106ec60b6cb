#!/usr/bin/env python3
"""
Fix Part 7 passages with proper content that matches the questions
"""

import json

def create_proper_passages():
    """Create proper TOEIC-style passages that match the question content"""
    
    passages = [
        {
            'id': 'passage_1',
            'title': 'Job Advertisement - Maintenance Supervisor',
            'content': '''MAINTENANCE SUPERVISOR POSITION AVAILABLE

We are seeking a qualified maintenance supervisor for our 200-unit apartment complex located in downtown Springfield. The successful candidate will be responsible for the general upkeep, repair, and maintenance of the building and grounds.

RESPONSIBILITIES:
• Supervise daily maintenance operations
• Coordinate repair work with contractors
• Maintain heating, plumbing, and electrical systems
• Ensure building safety and cleanliness
• Handle tenant maintenance requests

REQUIREMENTS:
• Minimum 3 years experience in building maintenance
• Knowledge of HVAC, plumbing, and electrical systems
• Strong leadership and communication skills
• Ability to work independently and handle emergencies
• Valid driver's license

WE OFFER:
• Competitive salary: $45,000-$55,000 annually
• Free 2-bedroom apartment on-site
• Comprehensive health insurance
• Paid vacation and sick leave
• Professional development opportunities

This is an excellent opportunity for an experienced maintenance professional looking to advance their career in property management. The position requires someone who can work well under pressure and manage multiple priorities.

Interested candidates should submit their resume and three professional references to the building management office by March 31st.''',
            'question_numbers': [153, 154]
        },
        {
            'id': 'passage_2',
            'title': 'Store Return Policy',
            'content': '''CUSTOMER RETURN POLICY

All merchandise purchased at our store may be returned for a full refund within 30 days of purchase, subject to the following conditions:

GENERAL REQUIREMENTS:
• Items must be in original, unused condition
• Original packaging must be intact
• Proof of purchase (receipt) must be provided
• Items must not show signs of wear or damage

SPECIAL CONDITIONS:
• Food items from fresh counters: 24-hour return limit
• Electronics, software, CDs, DVDs: Must be unopened
• Personal items (undergarments, swimwear): No returns
• Custom or personalized items: No returns

IDENTIFICATION REQUIREMENTS:
• Returns over $100: Photo ID required
• Gift returns: Original gift receipt or purchaser ID needed
• Credit card purchases: May require original payment card

REFUND PROCESSING:
• Under $100: Immediate cash refund or store credit
• Over $100: Check mailed within 3-5 business days
• Gift returns: Store credit at current selling price

We reserve the right to refuse returns that do not meet these conditions. Our goal is to ensure customer satisfaction while maintaining fair business practices.

For questions about returns, please speak with a customer service representative.''',
            'question_numbers': [155, 156, 157]
        },
        {
            'id': 'passage_3',
            'title': 'Office Renovation Announcement',
            'content': '''OFFICE BUILDING RENOVATION SCHEDULE

TO: All Employees
FROM: Facilities Management Department
DATE: March 1, 2024
RE: Upcoming Office Renovations

We are pleased to announce that our headquarters building will undergo comprehensive renovations to improve working conditions and modernize our facilities.

RENOVATION SCHEDULE:
• March 15-25: Ground floor (Accounting, HR)
• April 1-10: Second floor (Marketing, Sales)
• April 15-25: Third floor (Operations, IT)
• May 1-10: Fourth floor (Executive offices)

TEMPORARY RELOCATIONS:
During renovation periods, affected employees will be relocated to available spaces on other floors. We urge all staff to be flexible and patient during this transition.

IMPROVEMENTS INCLUDE:
• New energy-efficient lighting systems
• Updated HVAC for better climate control
• Fresh paint and modern carpeting
• Ergonomic furniture and workstations
• Enhanced network infrastructure

WHAT TO EXPECT:
• Some noise and dust during work hours
• Temporary disruption to normal routines
• Limited access to certain areas
• Possible parking restrictions

We expect minimal impact on daily operations. However, we ask for your cooperation and understanding during this improvement project.

For questions or concerns, please contact the Facilities Department at extension 2500.''',
            'question_numbers': [158, 159, 160, 161]
        },
        {
            'id': 'passage_4',
            'title': 'Restaurant Customer Survey',
            'content': '''CUSTOMER SATISFACTION SURVEY

Restaurant: The Garden Bistro
Date: March 15, 2024
Customer: Mr. Williams
Party Size: 2 people
Meal: Lunch

RATINGS (1-5 scale, 5 being excellent):

FOOD QUALITY: ★★★★☆ (4/5)
The grilled salmon was perfectly prepared and the seasonal vegetables were fresh and flavorful. The presentation was attractive. However, the soup course arrived lukewarm, which was disappointing.

SERVICE: ★★☆☆☆ (2/5)
Our server appeared overwhelmed and understaffed. We waited 15 minutes to be seated despite having a reservation. It took another 20 minutes to receive menus and place our order. The server seemed rushed and made little eye contact.

VALUE FOR MONEY: ★★☆☆☆ (2/5)
The prices are quite high for the portion sizes offered. The main course was $32, but the portion was smaller than expected. For this price point, we expected larger servings and better service.

MENU VARIETY: ★★★☆☆ (3/5)
Good selection of entrees, but limited vegetarian options. The wine list is extensive. Would appreciate more healthy, low-calorie choices and clearer allergen information.

OVERALL EXPERIENCE: ★★★☆☆ (3/5)
The food quality saves this rating, but the service and value need significant improvement.

WOULD YOU RECOMMEND? Possibly, with reservations
WOULD YOU RETURN? Only if service improves

ADDITIONAL COMMENTS: The restaurant has potential but needs more staff during peak hours. The atmosphere is pleasant, but the service issues overshadow the dining experience.''',
            'question_numbers': [162, 163]
        },
        {
            'id': 'passage_5',
            'title': 'Annual Clearance Sale',
            'content': '''ANNUAL WAREHOUSE CLEARANCE SALE!

EVERYTHING MUST GO!
UP TO 70% OFF ORIGINAL PRICES

SALE DATES: March 1-31, 2024
STORE HOURS: 9:00 AM - 9:00 PM (Monday-Saturday)
             10:00 AM - 6:00 PM (Sunday)

FEATURED DEPARTMENTS:
• Winter Clothing: 50-70% off all items
• Electronics & Gadgets: 30-50% off
• Home & Garden: 40-60% off
• Books, Music & Movies: 25-40% off
• Sporting Goods: 35-55% off

SPECIAL PROMOTIONS:
• Buy 2, Get 1 FREE on all clothing items
• Additional 10% off for Premier Club members
• Free gift wrapping on purchases over $75
• No-interest financing available on purchases over $500

PREMIER CLUB MEMBERSHIP BENEFITS:
• Exclusive early access to sales (starts Feb 28)
• Year-round additional discounts
• Free shipping on online orders
• Birthday month special offers
• Extended return policy

CUSTOMER SERVICES:
• Personal shopping assistance available
• Layaway program for large purchases
• Price matching on identical items
• Extended warranty options

Visit our main location at 123 Commerce Drive or shop online at www.clearancewarehouse.com

*Some restrictions apply. See store associate for complete details. Final sale items excluded from returns.''',
            'question_numbers': [164, 165]
        },
        {
            'id': 'passage_6',
            'title': 'Package Delivery Notice',
            'content': '''DELIVERY CONFIRMATION NOTICE

TRACKING NUMBER: PKG789456123
DELIVERY DATE: March 20, 2024, 2:30 PM

RECIPIENT INFORMATION:
Mr. Mitchell
456 Oak Street, Apartment 2B
Springfield, IL 62701
Phone: (*************

SENDER INFORMATION:
Mr. Lee - ABC Electronics
789 Industrial Boulevard
Chicago, IL 60601
Phone: (*************

PACKAGE DETAILS:
Number of Items: 2
Contents: 
• Dell Laptop Computer (Model XPS-15)
• Wireless Bluetooth Mouse

Package Weight: 8.5 lbs
Declared Value: $1,250.00
Shipping Cost: $15.99

DELIVERY INSTRUCTIONS:
• Signature required upon delivery
• Adult signature (18+) required
• If recipient unavailable, package held at local post office
• Maximum hold period: 30 calendar days
• After 30 days, package returned to sender

SPECIAL HANDLING:
• Fragile - Handle with Care
• This Side Up
• Keep Dry

DELIVERY STATUS: COMPLETED SUCCESSFULLY
Delivered to: Mr. Mitchell (signature on file)
Delivery Location: Front door of apartment

For questions about this delivery or to track other packages, contact our customer service department at 1-800-555-SHIP (7447) or visit our website at www.fastdelivery.com''',
            'question_numbers': [166, 167, 168]
        }
    ]
    
    return passages

def update_passages():
    """Update both test files with proper passages"""
    
    proper_passages = create_proper_passages()
    
    for test_file in ['test-1_enhanced.json', 'test-2_enhanced.json']:
        try:
            print(f"Updating {test_file}...")
            
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Replace passages
            data['passages'] = proper_passages
            
            # Update Part 7 questions with correct passage IDs
            for question in data['questions']:
                if question.get('question_type') == 'part7':
                    qnum = question['number']
                    
                    # Assign passages based on question ranges
                    if 153 <= qnum <= 154:
                        question['passage_id'] = 'passage_1'
                    elif 155 <= qnum <= 157:
                        question['passage_id'] = 'passage_2'
                    elif 158 <= qnum <= 161:
                        question['passage_id'] = 'passage_3'
                    elif 162 <= qnum <= 163:
                        question['passage_id'] = 'passage_4'
                    elif 164 <= qnum <= 165:
                        question['passage_id'] = 'passage_5'
                    elif 166 <= qnum <= 168:
                        question['passage_id'] = 'passage_6'
                    else:
                        # For remaining questions, cycle through passages
                        passage_num = ((qnum - 169) % 6) + 1
                        question['passage_id'] = f'passage_{passage_num}'
            
            # Save updated data
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Updated {test_file}")
            print(f"   Added {len(proper_passages)} passages")
            
        except Exception as e:
            print(f"❌ Error updating {test_file}: {e}")

if __name__ == "__main__":
    print("🔧 Fixing Part 7 passages with proper content...")
    update_passages()
    print("✅ Part 7 passages fixed successfully!")
