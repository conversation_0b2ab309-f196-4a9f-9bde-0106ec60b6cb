#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to examine the content of test-1.docx and test-2.docx
"""

try:
    from docx import Document
    print("python-docx is available")
except ImportError:
    print("python-docx not installed. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
    from docx import Document

def examine_document(filename):
    """Examine the content of a Word document"""
    print(f"\n{'='*50}")
    print(f"Examining: {filename}")
    print(f"{'='*50}")

    try:
        doc = Document(filename)

        print(f"Number of paragraphs: {len(doc.paragraphs)}")
        print(f"Number of tables: {len(doc.tables)}")

        # Look for Part 6 and Part 7
        part_6_line = None
        part_7_line = None

        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if 'Part 6' in text:
                part_6_line = i
                print(f"\nPART 6 found at line {i}: {text}")
            elif 'Part 7' in text:
                part_7_line = i
                print(f"\nPART 7 found at line {i}: {text}")

        # Show content around Part 6
        if part_6_line:
            print(f"\nContent around Part 6 (lines {part_6_line} to {part_6_line+15}):")
            for i in range(part_6_line, min(part_6_line+15, len(doc.paragraphs))):
                text = doc.paragraphs[i].text.strip()
                if text:
                    print(f"{i:3d}: {text[:120]}{'...' if len(text) > 120 else ''}")

        # Show content around Part 7
        if part_7_line:
            print(f"\nContent around Part 7 (lines {part_7_line} to {part_7_line+15}):")
            for i in range(part_7_line, min(part_7_line+15, len(doc.paragraphs))):
                text = doc.paragraphs[i].text.strip()
                if text:
                    print(f"{i:3d}: {text[:120]}{'...' if len(text) > 120 else ''}")

        # Show question number ranges
        question_numbers = []
        for para in doc.paragraphs:
            text = para.text.strip()
            import re
            match = re.match(r'^(\d+)\.', text)
            if match:
                question_numbers.append(int(match.group(1)))

        if question_numbers:
            print(f"\nQuestion numbers found: {min(question_numbers)} to {max(question_numbers)} ({len(question_numbers)} total)")

    except Exception as e:
        print(f"Error reading {filename}: {e}")

if __name__ == "__main__":
    examine_document("test-1.docx")
    examine_document("test-2.docx")
