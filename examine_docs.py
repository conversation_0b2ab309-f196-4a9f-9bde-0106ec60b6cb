#!/usr/bin/env python3
"""
Script to examine the content of test-1.docx and test-2.docx
"""

try:
    from docx import Document
    print("python-docx is available")
except ImportError:
    print("python-docx not installed. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
    from docx import Document

def examine_document(filename):
    """Examine the content of a Word document"""
    print(f"\n{'='*50}")
    print(f"Examining: {filename}")
    print(f"{'='*50}")
    
    try:
        doc = Document(filename)
        
        print(f"Number of paragraphs: {len(doc.paragraphs)}")
        print(f"Number of tables: {len(doc.tables)}")
        
        print("\nFirst 20 paragraphs:")
        for i, para in enumerate(doc.paragraphs[:20]):
            if para.text.strip():
                print(f"{i+1:2d}: {para.text[:100]}{'...' if len(para.text) > 100 else ''}")
        
        if doc.tables:
            print(f"\nFirst table structure:")
            table = doc.tables[0]
            print(f"Rows: {len(table.rows)}, Columns: {len(table.columns)}")
            
            # Show first few rows
            for i, row in enumerate(table.rows[:5]):
                row_text = [cell.text.strip() for cell in row.cells]
                print(f"Row {i+1}: {row_text}")
                
    except Exception as e:
        print(f"Error reading {filename}: {e}")

if __name__ == "__main__":
    examine_document("test-1.docx")
    examine_document("test-2.docx")
