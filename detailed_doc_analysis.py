#!/usr/bin/env python3
"""
Detailed analysis of the original Word documents to find missing questions
and properly format Part 6 questions with blanks
"""

from docx import Document
import re
import json

def analyze_document_structure(filename):
    """Analyze document structure in detail"""
    print(f"\n{'='*60}")
    print(f"DETAILED ANALYSIS: {filename}")
    print(f"{'='*60}")
    
    doc = Document(filename)
    
    # Track all questions found
    questions_found = []
    part_6_content = []
    part_7_content = []
    
    current_part = None
    paragraph_buffer = []
    
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        if not text:
            continue
        
        # Check for Part indicators
        if 'Part 6' in text or 'PART 6' in text:
            current_part = 6
            print(f"\n🔍 PART 6 FOUND at line {i}: {text}")
            paragraph_buffer = []
            continue
        elif 'Part 7' in text or 'PART 7' in text:
            current_part = 7
            print(f"\n🔍 PART 7 FOUND at line {i}: {text}")
            continue
        
        # Check for question numbers
        question_match = re.match(r'^(\d+)\.\s*(.+)', text)
        if question_match:
            question_num = int(question_match.group(1))
            question_text = question_match.group(2)
            questions_found.append(question_num)
            
            print(f"Q{question_num}: {question_text[:80]}...")
            
            # Store Part 6 content
            if current_part == 6:
                part_6_content.append({
                    'number': question_num,
                    'question': question_text,
                    'paragraph_context': '\n'.join(paragraph_buffer) if paragraph_buffer else None
                })
        
        # Look for paragraph content (potential Part 6 context)
        elif current_part == 6 and len(text) > 30 and not re.match(r'^\([A-D]\)', text):
            # This might be paragraph content for Part 6
            paragraph_buffer.append(text)
            print(f"  📝 Paragraph content: {text[:60]}...")
        
        # Look for blanks in text (indicated by -------)
        if '-------' in text or '____' in text or '_____' in text:
            print(f"  🔲 BLANK FOUND in: {text[:80]}...")
    
    print(f"\n📊 SUMMARY:")
    print(f"Questions found: {sorted(questions_found)}")
    print(f"Question count: {len(questions_found)}")
    print(f"Question range: {min(questions_found) if questions_found else 'None'} to {max(questions_found) if questions_found else 'None'}")
    
    # Check for missing questions
    if questions_found:
        expected_range = list(range(min(questions_found), max(questions_found) + 1))
        missing = [q for q in expected_range if q not in questions_found]
        if missing:
            print(f"❌ MISSING QUESTIONS: {missing}")
        else:
            print("✅ No missing questions in range")
    
    print(f"\nPart 6 content found: {len(part_6_content)} items")
    for item in part_6_content:
        print(f"  Q{item['number']}: {item['question'][:50]}...")
        if item['paragraph_context']:
            print(f"    Context: {len(item['paragraph_context'])} chars")
    
    return questions_found, part_6_content

def compare_with_current_data(filename):
    """Compare with our current parsed data"""
    print(f"\n{'='*60}")
    print(f"COMPARING WITH CURRENT DATA")
    print(f"{'='*60}")
    
    # Load current enhanced data
    if filename == 'test-1.docx':
        json_file = 'test-1_enhanced.json'
    else:
        json_file = 'test-2_enhanced.json'
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            current_data = json.load(f)
        
        current_questions = [q['number'] for q in current_data['questions']]
        print(f"Current data has {len(current_questions)} questions")
        print(f"Range: {min(current_questions)} to {max(current_questions)}")
        
        # Check Part 6 questions
        part6_questions = [q for q in current_data['questions'] if q.get('question_type') == 'part6']
        print(f"Part 6 questions: {len(part6_questions)}")
        
        for q in part6_questions:
            print(f"  Q{q['number']}: {q['question'][:50]}...")
            if q.get('paragraph_text'):
                # Check if paragraph has blanks marked
                if '-------' in q['paragraph_text'] or '____' in q['paragraph_text']:
                    print(f"    ✅ Has blanks in paragraph")
                else:
                    print(f"    ❌ NO BLANKS in paragraph")
        
        return current_questions, part6_questions
        
    except FileNotFoundError:
        print(f"❌ Current data file {json_file} not found")
        return [], []

def find_blanks_in_text(text):
    """Find blank patterns in text"""
    blank_patterns = [
        r'-{3,}',  # Three or more dashes
        r'_{3,}',  # Three or more underscores
        r'\(\s*\)',  # Empty parentheses
        r'\[\s*\]',  # Empty brackets
    ]
    
    blanks = []
    for i, pattern in enumerate(blank_patterns):
        matches = list(re.finditer(pattern, text))
        for match in matches:
            blanks.append({
                'pattern': pattern,
                'start': match.start(),
                'end': match.end(),
                'text': match.group()
            })
    
    return blanks

if __name__ == "__main__":
    for doc_file in ['test-1.docx', 'test-2.docx']:
        if doc_file == 'test-1.docx':
            print("🔍 ANALYZING TEST 1")
        else:
            print("🔍 ANALYZING TEST 2")
        
        # Analyze original document
        doc_questions, doc_part6 = analyze_document_structure(doc_file)
        
        # Compare with current data
        current_questions, current_part6 = compare_with_current_data(doc_file)
        
        # Find discrepancies
        print(f"\n📋 DISCREPANCY ANALYSIS:")
        missing_from_current = [q for q in doc_questions if q not in current_questions]
        extra_in_current = [q for q in current_questions if q not in doc_questions]
        
        if missing_from_current:
            print(f"❌ Missing from current data: {missing_from_current}")
        if extra_in_current:
            print(f"⚠️  Extra in current data: {extra_in_current}")
        if not missing_from_current and not extra_in_current:
            print("✅ Question numbers match!")
        
        print(f"\n" + "="*60)
