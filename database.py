"""
Database operations and data loading for the English Test API
"""

import json
from typing import Dict, List, Optional
from models import Test, Question, QuestionOption, TestInfo

class TestDatabase:
    def __init__(self):
        self.tests: Dict[str, Test] = {}
        self.load_tests()
    
    def load_tests(self):
        """Load test data from JSON files"""
        try:
            # Load Test 1
            with open('test1_data.json', 'r', encoding='utf-8') as f:
                test1_data = json.load(f)
            
            test1_questions = []
            for q_data in test1_data['questions']:
                options = [QuestionOption(**opt) for opt in q_data['options']]
                question = Question(
                    number=q_data['number'],
                    question=q_data['question'],
                    options=options,
                    correct_answer=q_data.get('correct_answer')
                )
                test1_questions.append(question)
            
            self.tests['test1'] = Test(
                id='test1',
                name='English Test 1',
                description='TOEIC-style English proficiency test with grammar and vocabulary questions',
                questions=test1_questions,
                passages=test1_data.get('passages', [])
            )
            
            # Load Test 2
            with open('test2_data.json', 'r', encoding='utf-8') as f:
                test2_data = json.load(f)
            
            test2_questions = []
            for q_data in test2_data['questions']:
                options = [QuestionOption(**opt) for opt in q_data['options']]
                question = Question(
                    number=q_data['number'],
                    question=q_data['question'],
                    options=options,
                    correct_answer=q_data.get('correct_answer')
                )
                test2_questions.append(question)
            
            self.tests['test2'] = Test(
                id='test2',
                name='English Test 2',
                description='TOEIC-style English proficiency test with grammar and vocabulary questions',
                questions=test2_questions,
                passages=test2_data.get('passages', [])
            )
            
            print(f"Loaded {len(self.tests)} tests successfully")
            for test_id, test in self.tests.items():
                print(f"  {test_id}: {len(test.questions)} questions")
                
        except Exception as e:
            print(f"Error loading test data: {e}")
            # Create empty tests as fallback
            self.tests = {}
    
    def get_test_info(self, test_id: str) -> Optional[TestInfo]:
        """Get basic information about a test"""
        if test_id not in self.tests:
            return None
        
        test = self.tests[test_id]
        return TestInfo(
            id=test.id,
            name=test.name,
            description=test.description,
            total_questions=len(test.questions),
            estimated_time=len(test.questions) * 2  # 2 minutes per question
        )
    
    def get_all_tests_info(self) -> List[TestInfo]:
        """Get information about all available tests"""
        return [self.get_test_info(test_id) for test_id in self.tests.keys()]
    
    def get_test(self, test_id: str) -> Optional[Test]:
        """Get a complete test by ID"""
        return self.tests.get(test_id)
    
    def get_question(self, test_id: str, question_number: int) -> Optional[Question]:
        """Get a specific question from a test"""
        test = self.get_test(test_id)
        if not test:
            return None
        
        for question in test.questions:
            if question.number == question_number:
                return question
        return None
    
    def get_questions_range(self, test_id: str, start: int, end: int) -> List[Question]:
        """Get a range of questions from a test"""
        test = self.get_test(test_id)
        if not test:
            return []
        
        return [q for q in test.questions if start <= q.number <= end]

# Global database instance
db = TestDatabase()
