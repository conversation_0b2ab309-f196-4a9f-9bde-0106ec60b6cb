#!/usr/bin/env python3
"""
Enhanced parser for English test documents with proper Part 6 and Part 7 support
"""

from docx import Document
import re
import json
from typing import List, Dict, Any, Optional

def determine_question_type(question_number: int) -> str:
    """Determine question type based on question number"""
    if 101 <= question_number <= 140:
        return "part5"
    elif 141 <= question_number <= 152:
        return "part6"
    elif 153 <= question_number <= 200:
        return "part7"
    else:
        return "part5"  # default

def parse_enhanced_test_document(filename: str) -> Dict[str, Any]:
    """Parse a test document with enhanced support for different question types"""
    doc = Document(filename)
    questions = []
    passages = []
    current_question = None
    current_passage = None
    passage_counter = 0
    
    # Track current context
    in_part6_paragraph = False
    in_part7_passage = False
    current_paragraph_text = ""
    current_passage_text = ""
    current_passage_questions = []
    
    for i, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        if not text:
            continue
        
        # Check for Part indicators
        if 'Part 6' in text:
            in_part6_paragraph = True
            in_part7_passage = False
            print(f"Found Part 6 at line {i}")
            continue
        elif 'Part 7' in text:
            in_part6_paragraph = False
            in_part7_passage = True
            print(f"Found Part 7 at line {i}")
            continue
        
        # Check if this is a question number
        question_match = re.match(r'^(\d+)\.\s+(.+)', text)
        if question_match:
            # Save previous question if exists
            if current_question:
                questions.append(current_question)
            
            # Save previous passage if we're transitioning from Part 7
            if current_passage_text and current_passage_questions:
                passage_counter += 1
                passages.append({
                    'id': f'passage_{passage_counter}',
                    'title': f'Reading Passage {passage_counter}',
                    'content': current_passage_text.strip(),
                    'question_numbers': current_passage_questions.copy()
                })
                current_passage_text = ""
                current_passage_questions = []
            
            # Start new question
            question_num = int(question_match.group(1))
            question_text = question_match.group(2)
            question_type = determine_question_type(question_num)
            
            current_question = {
                'number': question_num,
                'question': question_text,
                'options': [],
                'correct_answer': None,
                'question_type': question_type
            }
            
            # Add type-specific fields
            if question_type == "part6":
                current_question['paragraph_text'] = current_paragraph_text
                current_question['blank_positions'] = []
            elif question_type == "part7":
                current_question['passage_id'] = f'passage_{passage_counter + 1}'
                current_passage_questions.append(question_num)
        
        # Check if this is an answer option
        elif current_question and re.match(r'^\([A-D]\)', text):
            option_match = re.match(r'^\(([A-D])\)\s*(.+)', text)
            if option_match:
                option_letter = option_match.group(1)
                option_text = option_match.group(2)
                current_question['options'].append({
                    'letter': option_letter,
                    'text': option_text
                })
        
        # Collect paragraph text for Part 6
        elif in_part6_paragraph and not question_match and not re.match(r'^\([A-D]\)', text):
            # This might be paragraph content for Part 6
            if len(text) > 50:  # Assume longer texts are paragraph content
                current_paragraph_text += text + "\n"
        
        # Collect passage text for Part 7
        elif in_part7_passage and not question_match and not re.match(r'^\([A-D]\)', text):
            # This might be passage content for Part 7
            if len(text) > 30:  # Assume longer texts are passage content
                current_passage_text += text + "\n"
    
    # Don't forget the last question
    if current_question:
        questions.append(current_question)
    
    # Don't forget the last passage
    if current_passage_text and current_passage_questions:
        passage_counter += 1
        passages.append({
            'id': f'passage_{passage_counter}',
            'title': f'Reading Passage {passage_counter}',
            'content': current_passage_text.strip(),
            'question_numbers': current_passage_questions
        })
    
    return {
        'questions': questions,
        'passages': passages
    }

def parse_table_passages(filename: str) -> List[Dict[str, Any]]:
    """Extract reading passages from tables in the document"""
    doc = Document(filename)
    passages = []
    passage_counter = 0
    
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                text = cell.text.strip()
                if len(text) > 200:  # Assume passages are longer texts
                    passage_counter += 1
                    passages.append({
                        'id': f'table_passage_{passage_counter}',
                        'title': f'Reading Passage {passage_counter}',
                        'content': text,
                        'question_numbers': []  # Will be assigned later
                    })
    
    return passages

def enhance_existing_data(filename: str) -> Dict[str, Any]:
    """Enhance existing parsed data with better structure"""
    # Load existing data
    if filename == 'test-1.docx':
        json_filename = 'test1_data.json'
    elif filename == 'test-2.docx':
        json_filename = 'test2_data.json'
    else:
        json_filename = filename.replace('.docx', '_data.json')

    try:
        with open(json_filename, 'r', encoding='utf-8') as f:
            existing_data = json.load(f)
    except FileNotFoundError:
        print(f"No existing data found for {filename} (looking for {json_filename})")
        return parse_enhanced_test_document(filename)
    
    # Enhance questions with type information
    enhanced_questions = []
    for q in existing_data['questions']:
        question_type = determine_question_type(q['number'])
        enhanced_q = q.copy()
        enhanced_q['question_type'] = question_type
        
        if question_type == "part7":
            # Assign passage ID for Part 7 questions
            if 153 <= q['number'] <= 200:
                # Simple assignment - could be more sophisticated
                passage_id = f"passage_1"
                enhanced_q['passage_id'] = passage_id
        
        enhanced_questions.append(enhanced_q)
    
    # Convert existing passages to new format
    enhanced_passages = []
    if existing_data.get('passages'):
        for i, passage_text in enumerate(existing_data['passages']):
            enhanced_passages.append({
                'id': f'passage_{i+1}',
                'title': f'Reading Passage {i+1}',
                'content': passage_text,
                'question_numbers': list(range(153, 201))  # Assign all Part 7 questions
            })
    
    return {
        'questions': enhanced_questions,
        'passages': enhanced_passages
    }

if __name__ == "__main__":
    # Enhance existing test data
    print("Enhancing test data with proper Part 6 and Part 7 support...")
    
    for test_file in ['test-1.docx', 'test-2.docx']:
        print(f"\nProcessing {test_file}...")
        enhanced_data = enhance_existing_data(test_file)
        
        output_file = test_file.replace('.docx', '_enhanced.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, indent=2, ensure_ascii=False)
        
        print(f"Enhanced data saved to {output_file}")
        print(f"Questions: {len(enhanced_data['questions'])}")
        print(f"Passages: {len(enhanced_data['passages'])}")
        
        # Show question type distribution
        type_counts = {}
        for q in enhanced_data['questions']:
            qtype = q.get('question_type', 'unknown')
            type_counts[qtype] = type_counts.get(qtype, 0) + 1
        
        print(f"Question types: {type_counts}")
