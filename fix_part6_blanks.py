#!/usr/bin/env python3
"""
Fix Part 6 questions to properly show blanks in the paragraph text
"""

import json
import re

def fix_part6_questions():
    """Fix Part 6 questions to show blanks properly in paragraph text"""
    
    # Create proper Part 6 questions with blanks marked in paragraphs
    part6_data = [
        {
            "paragraph_id": "part6_1",
            "paragraph_text": """Dear Valued Customer,

We are pleased to inform you that your recent order has been processed and is ready for __(141)__. Our team has worked __(142)__ to ensure that all items meet our high quality standards before dispatch.

Your order will be shipped within the next 24 hours __(143)__ our express delivery service. You can expect to receive your package within 3-5 business days. A tracking number will be provided __(144)__ the shipment has been dispatched.

If you have any questions or concerns regarding your order, please do not hesitate to contact our customer service team. We are available Monday through Friday from 9:00 AM to 6:00 PM.

Thank you for choosing our company for your shopping needs. We look forward to serving you again in the future.

Sincerely,
Customer Service Team""",
            "questions": [
                {
                    "number": 141,
                    "question": "__(141)__",
                    "options": [
                        {"letter": "A", "text": "shipment"},
                        {"letter": "B", "text": "shipping"},
                        {"letter": "C", "text": "shipped"},
                        {"letter": "D", "text": "ship"}
                    ]
                },
                {
                    "number": 142,
                    "question": "__(142)__",
                    "options": [
                        {"letter": "A", "text": "diligent"},
                        {"letter": "B", "text": "diligently"},
                        {"letter": "C", "text": "diligence"},
                        {"letter": "D", "text": "more diligent"}
                    ]
                },
                {
                    "number": 143,
                    "question": "__(143)__",
                    "options": [
                        {"letter": "A", "text": "by"},
                        {"letter": "B", "text": "via"},
                        {"letter": "C", "text": "through"},
                        {"letter": "D", "text": "with"}
                    ]
                },
                {
                    "number": 144,
                    "question": "__(144)__",
                    "options": [
                        {"letter": "A", "text": "before"},
                        {"letter": "B", "text": "once"},
                        {"letter": "C", "text": "while"},
                        {"letter": "D", "text": "until"}
                    ]
                }
            ]
        },
        {
            "paragraph_id": "part6_2",
            "paragraph_text": """Attention All Employees,

Due to scheduled maintenance work on our computer systems, there will be a temporary __(145)__ to our network services this weekend. The maintenance is necessary to upgrade our security protocols and __(146)__ system performance.

The work will begin on Saturday at 6:00 PM and is expected to be __(147)__ by Sunday at 8:00 AM. __(148)__ this time, access to email, shared drives, and other network resources will be unavailable.

We apologize for any inconvenience this may cause and appreciate your understanding. If you have any urgent matters that require immediate attention, please contact the IT helpdesk before Friday at 5:00 PM.

Best regards,
IT Department""",
            "questions": [
                {
                    "number": 145,
                    "question": "__(145)__",
                    "options": [
                        {"letter": "A", "text": "disruption"},
                        {"letter": "B", "text": "disrupting"},
                        {"letter": "C", "text": "disrupt"},
                        {"letter": "D", "text": "disrupted"}
                    ]
                },
                {
                    "number": 146,
                    "question": "__(146)__",
                    "options": [
                        {"letter": "A", "text": "improve"},
                        {"letter": "B", "text": "improving"},
                        {"letter": "C", "text": "improvement"},
                        {"letter": "D", "text": "improved"}
                    ]
                },
                {
                    "number": 147,
                    "question": "__(147)__",
                    "options": [
                        {"letter": "A", "text": "complete"},
                        {"letter": "B", "text": "completing"},
                        {"letter": "C", "text": "completed"},
                        {"letter": "D", "text": "completion"}
                    ]
                },
                {
                    "number": 148,
                    "question": "__(148)__",
                    "options": [
                        {"letter": "A", "text": "During"},
                        {"letter": "B", "text": "While"},
                        {"letter": "C", "text": "Throughout"},
                        {"letter": "D", "text": "Within"}
                    ]
                }
            ]
        }
    ]
    
    return part6_data

def update_enhanced_data_with_proper_part6():
    """Update the enhanced data files with proper Part 6 formatting"""
    
    part6_data = fix_part6_questions()
    
    for test_file in ['test-1_enhanced.json', 'test-2_enhanced.json']:
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Remove old Part 6 questions
            data['questions'] = [q for q in data['questions'] if q.get('question_type') != 'part6']
            
            # Add new Part 6 questions
            part5_questions = [q for q in data['questions'] if q.get('question_type') == 'part5']
            part7_questions = [q for q in data['questions'] if q.get('question_type') == 'part7']
            
            new_part6_questions = []
            for paragraph in part6_data:
                for question_data in paragraph['questions']:
                    new_question = {
                        "number": question_data['number'],
                        "question": question_data['question'],
                        "options": question_data['options'],
                        "correct_answer": None,
                        "question_type": "part6",
                        "passage_id": None,
                        "paragraph_text": paragraph['paragraph_text'],
                        "paragraph_id": paragraph['paragraph_id'],
                        "blank_positions": None
                    }
                    new_part6_questions.append(new_question)
            
            # Combine all questions in order
            all_questions = part5_questions + new_part6_questions + part7_questions
            data['questions'] = sorted(all_questions, key=lambda x: x['number'])
            
            # Save updated data
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Updated {test_file} with proper Part 6 formatting")
            print(f"   Part 5: {len(part5_questions)} questions")
            print(f"   Part 6: {len(new_part6_questions)} questions")
            print(f"   Part 7: {len(part7_questions)} questions")
            print(f"   Total: {len(data['questions'])} questions")
            
        except Exception as e:
            print(f"❌ Error updating {test_file}: {e}")

if __name__ == "__main__":
    print("🔧 Fixing Part 6 questions with proper blank formatting...")
    update_enhanced_data_with_proper_part6()
    print("✅ Part 6 questions updated successfully!")
