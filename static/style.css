/* English Test System Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Screen Management */
.screen {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

.screen.active {
    display: block;
}

/* Test Selection */
.tests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.test-card {
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f9f9f9;
}

.test-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.test-card.selected {
    border-color: #667eea;
    background: #f0f4ff;
}

.test-card h3 {
    color: #333;
    margin-bottom: 10px;
}

.test-card p {
    color: #666;
    margin-bottom: 15px;
}

.test-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #888;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.button-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

/* Test Taking Interface */
.test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
}

.test-progress {
    flex: 1;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    margin-top: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    transition: width 0.3s ease;
    width: 0%;
}

.test-timer {
    font-size: 1.2rem;
    font-weight: bold;
    color: #667eea;
}

/* Question Container */
.question-container {
    margin-bottom: 30px;
}

.question {
    margin-bottom: 30px;
}

.question h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
    line-height: 1.5;
}

/* Question Type Headers */
.question-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.question-type {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.question-header h3 {
    margin: 0;
    color: #333;
}

/* Part 6 Specific Styles */
.part6-question .paragraph-context {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.part6-question .paragraph-context h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1rem;
}

.paragraph-text {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #ddd;
    line-height: 1.6;
}

.paragraph-text p {
    margin-bottom: 10px;
}

.paragraph-text p:last-child {
    margin-bottom: 0;
}

/* Part 7 Specific Styles */
.part7-question .passage-context {
    background: #f0f4ff;
    border: 1px solid #c3d4ff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.part7-question .passage-context h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
}

.passage-text {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #c3d4ff;
    line-height: 1.7;
}

.passage-text p {
    margin-bottom: 12px;
    text-align: justify;
}

.passage-text p:last-child {
    margin-bottom: 0;
}

/* Question Text Styling */
.question-text {
    margin-bottom: 20px;
}

.question-text p {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
    line-height: 1.5;
}

.options {
    list-style: none;
}

.option {
    margin-bottom: 15px;
}

.option label {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option label:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.option input[type="radio"] {
    margin-right: 15px;
    transform: scale(1.2);
}

.option input[type="radio"]:checked + span {
    font-weight: 500;
}

.option label.selected {
    border-color: #667eea;
    background: #f0f4ff;
}

/* Navigation */
.navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
}

/* Instructions */
.instruction-box {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.instruction-box h3 {
    color: #667eea;
    margin-bottom: 15px;
}

.instruction-box ul {
    padding-left: 20px;
}

.instruction-box li {
    margin-bottom: 8px;
}

/* Review Screen */
.review-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.review-stat {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.review-stat h4 {
    color: #667eea;
    margin-bottom: 10px;
}

.review-stat .number {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.review-questions {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

.review-question {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background 0.3s ease;
}

.review-question:hover {
    background: #f8f9fa;
}

.review-question:last-child {
    border-bottom: none;
}

.review-question.answered {
    border-left: 4px solid #28a745;
}

.review-question.unanswered {
    border-left: 4px solid #dc3545;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e0e0e0;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Screen */
.results-summary {
    text-align: center;
    margin-bottom: 30px;
}

.results-summary h3 {
    color: #667eea;
    margin-bottom: 30px;
    font-size: 2rem;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.result-stat {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e0e0e0;
}

.result-stat h4 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1rem;
}

.result-stat .number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #333;
}

.completion-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 20px;
    color: #155724;
}

.completion-message p {
    margin-bottom: 10px;
}

.completion-message p:last-child {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .screen {
        padding: 20px;
    }

    .test-header {
        flex-direction: column;
        gap: 15px;
    }

    .navigation {
        flex-direction: column;
    }

    .button-group {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .result-stats {
        grid-template-columns: 1fr;
    }

    .result-stat .number {
        font-size: 2rem;
    }
}
