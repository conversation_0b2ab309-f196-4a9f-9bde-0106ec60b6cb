// English Test System JavaScript

class TestApp {
    constructor() {
        this.currentTest = null;
        this.currentQuestionIndex = 0;
        this.questions = [];
        this.userAnswers = {};
        this.startTime = null;
        this.timer = null;
        
        this.initializeApp();
    }
    
    async initializeApp() {
        this.bindEvents();
        await this.loadTests();
        this.hideLoading();
    }
    
    bindEvents() {
        // Test selection
        document.getElementById('start-test').addEventListener('click', () => this.startTest());
        document.getElementById('back-to-selection').addEventListener('click', () => this.showTestSelection());
        
        // Test navigation
        document.getElementById('prev-question').addEventListener('click', () => this.previousQuestion());
        document.getElementById('next-question').addEventListener('click', () => this.nextQuestion());
        document.getElementById('review-answers').addEventListener('click', () => this.showReview());
        document.getElementById('submit-test').addEventListener('click', () => this.showReview());
        
        // Review screen
        document.getElementById('back-to-test').addEventListener('click', () => this.backToTest());
        document.getElementById('final-submit').addEventListener('click', () => this.submitTest());
        
        // Results screen
        document.getElementById('take-another-test').addEventListener('click', () => this.showTestSelection());
    }
    
    showLoading() {
        document.getElementById('loading').classList.remove('hidden');
    }
    
    hideLoading() {
        document.getElementById('loading').classList.add('hidden');
    }
    
    showScreen(screenId) {
        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        
        // Show target screen
        document.getElementById(screenId).classList.add('active');
    }
    
    async loadTests() {
        try {
            this.showLoading();
            const response = await fetch('/api/tests');
            const tests = await response.json();
            
            this.renderTestSelection(tests);
        } catch (error) {
            console.error('Error loading tests:', error);
            this.showError('Failed to load tests. Please refresh the page.');
        } finally {
            this.hideLoading();
        }
    }
    
    renderTestSelection(tests) {
        const testsContainer = document.getElementById('tests-list');
        testsContainer.innerHTML = '';
        
        tests.forEach(test => {
            const testCard = document.createElement('div');
            testCard.className = 'test-card';
            testCard.dataset.testId = test.id;
            
            testCard.innerHTML = `
                <h3>${test.name}</h3>
                <p>${test.description}</p>
                <div class="test-meta">
                    <span>${test.total_questions} questions</span>
                    <span>~${test.estimated_time} minutes</span>
                </div>
            `;
            
            testCard.addEventListener('click', () => this.selectTest(test));
            testsContainer.appendChild(testCard);
        });
    }
    
    selectTest(test) {
        // Remove previous selection
        document.querySelectorAll('.test-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Select current test
        document.querySelector(`[data-test-id="${test.id}"]`).classList.add('selected');
        this.currentTest = test;
        
        // Show instructions
        this.showTestInstructions(test);
    }
    
    showTestInstructions(test) {
        const testInfo = document.getElementById('test-info');
        testInfo.innerHTML = `
            <div class="instruction-box">
                <h3>${test.name}</h3>
                <p><strong>Description:</strong> ${test.description}</p>
                <p><strong>Number of Questions:</strong> ${test.total_questions}</p>
                <p><strong>Estimated Time:</strong> ${test.estimated_time} minutes</p>
            </div>
        `;
        
        this.showScreen('test-instructions');
    }
    
    showTestSelection() {
        this.currentTest = null;
        this.showScreen('test-selection');
    }
    
    async startTest() {
        if (!this.currentTest) {
            this.showError('Please select a test first.');
            return;
        }

        try {
            this.showLoading();

            // Load test questions
            const response = await fetch(`/api/test/${this.currentTest.id}/questions`);
            this.questions = await response.json();

            // Initialize test state
            this.currentQuestionIndex = 0;
            this.userAnswers = {};
            this.startTime = new Date();

            // Start timer
            this.startTimer();

            // Show first question
            this.showTestTaking();
            this.renderCurrentQuestion();

        } catch (error) {
            console.error('Error starting test:', error);
            this.showError('Failed to start test. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    showTestTaking() {
        this.showScreen('test-taking');
        document.getElementById('total-questions').textContent = this.questions.length;
        this.updateProgress();
    }

    startTimer() {
        this.timer = setInterval(() => {
            const elapsed = Math.floor((new Date() - this.startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    renderCurrentQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        if (!question) return;

        const questionContainer = document.getElementById('question-content');
        questionContainer.innerHTML = `
            <div class="question">
                <h3>Question ${question.number}: ${question.question}</h3>
                <ul class="options">
                    ${question.options.map(option => `
                        <li class="option">
                            <label>
                                <input type="radio" name="question-${question.number}" value="${option.letter}">
                                <span>(${option.letter}) ${option.text}</span>
                            </label>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;

        // Restore previous answer if exists
        const previousAnswer = this.userAnswers[question.number];
        if (previousAnswer) {
            const radio = questionContainer.querySelector(`input[value="${previousAnswer}"]`);
            if (radio) {
                radio.checked = true;
                radio.closest('label').classList.add('selected');
            }
        }

        // Add event listeners for answer selection
        questionContainer.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                // Remove previous selection styling
                questionContainer.querySelectorAll('label').forEach(label => {
                    label.classList.remove('selected');
                });

                // Add selection styling
                e.target.closest('label').classList.add('selected');

                // Save answer
                this.userAnswers[question.number] = e.target.value;
            });
        });

        this.updateProgress();
        this.updateNavigation();
    }

    updateProgress() {
        const current = this.currentQuestionIndex + 1;
        const total = this.questions.length;

        document.getElementById('current-question').textContent = current;
        document.getElementById('total-questions').textContent = total;

        const progressPercent = (current / total) * 100;
        document.getElementById('progress-fill').style.width = `${progressPercent}%`;
    }

    updateNavigation() {
        const prevBtn = document.getElementById('prev-question');
        const nextBtn = document.getElementById('next-question');
        const submitBtn = document.getElementById('submit-test');

        // Previous button
        prevBtn.disabled = this.currentQuestionIndex === 0;

        // Next/Submit button
        const isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;
        if (isLastQuestion) {
            nextBtn.style.display = 'none';
            submitBtn.style.display = 'inline-block';
        } else {
            nextBtn.style.display = 'inline-block';
            submitBtn.style.display = 'none';
        }
    }

    previousQuestion() {
        if (this.currentQuestionIndex > 0) {
            this.currentQuestionIndex--;
            this.renderCurrentQuestion();
        }
    }

    nextQuestion() {
        if (this.currentQuestionIndex < this.questions.length - 1) {
            this.currentQuestionIndex++;
            this.renderCurrentQuestion();
        }
    }

    showReview() {
        this.showScreen('review-screen');
        this.renderReview();
    }

    renderReview() {
        const reviewContent = document.getElementById('review-content');
        const answeredCount = Object.keys(this.userAnswers).length;
        const totalCount = this.questions.length;
        const unansweredCount = totalCount - answeredCount;

        reviewContent.innerHTML = `
            <div class="review-summary">
                <div class="review-stat">
                    <h4>Total Questions</h4>
                    <div class="number">${totalCount}</div>
                </div>
                <div class="review-stat">
                    <h4>Answered</h4>
                    <div class="number">${answeredCount}</div>
                </div>
                <div class="review-stat">
                    <h4>Unanswered</h4>
                    <div class="number">${unansweredCount}</div>
                </div>
            </div>

            <div class="review-questions">
                ${this.questions.map(question => {
                    const isAnswered = this.userAnswers[question.number];
                    const answerText = isAnswered ?
                        question.options.find(opt => opt.letter === isAnswered)?.text || 'Unknown' :
                        'Not answered';

                    return `
                        <div class="review-question ${isAnswered ? 'answered' : 'unanswered'}"
                             data-question-index="${this.questions.indexOf(question)}">
                            <strong>Q${question.number}:</strong> ${question.question.substring(0, 80)}...
                            <br>
                            <small><strong>Answer:</strong> ${isAnswered ? `(${isAnswered}) ${answerText}` : answerText}</small>
                        </div>
                    `;
                }).join('')}
            </div>
        `;

        // Add click handlers to jump to questions
        reviewContent.querySelectorAll('.review-question').forEach(element => {
            element.addEventListener('click', () => {
                const questionIndex = parseInt(element.dataset.questionIndex);
                this.currentQuestionIndex = questionIndex;
                this.backToTest();
            });
        });
    }

    backToTest() {
        this.showScreen('test-taking');
        this.renderCurrentQuestion();
    }

    async submitTest() {
        try {
            this.showLoading();
            this.stopTimer();

            // Prepare submission data
            const answers = Object.entries(this.userAnswers).map(([questionNumber, answer]) => ({
                question_number: parseInt(questionNumber),
                selected_answer: answer
            }));

            const submission = {
                test_id: this.currentTest.id,
                answers: answers
            };

            // Submit to API
            const response = await fetch(`/api/test/${this.currentTest.id}/submit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(submission)
            });

            const result = await response.json();
            this.showResults(result);

        } catch (error) {
            console.error('Error submitting test:', error);
            this.showError('Failed to submit test. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    showResults(result) {
        this.showScreen('results-screen');

        const resultsContent = document.getElementById('results-content');
        const elapsedTime = Math.floor((new Date() - this.startTime) / 1000);
        const minutes = Math.floor(elapsedTime / 60);
        const seconds = elapsedTime % 60;

        resultsContent.innerHTML = `
            <div class="results-summary">
                <h3>Test Completed!</h3>
                <div class="result-stats">
                    <div class="result-stat">
                        <h4>Questions Answered</h4>
                        <div class="number">${result.answered_questions} / ${result.total_questions}</div>
                    </div>
                    <div class="result-stat">
                        <h4>Time Taken</h4>
                        <div class="number">${minutes}:${seconds.toString().padStart(2, '0')}</div>
                    </div>
                    ${result.score !== null ? `
                        <div class="result-stat">
                            <h4>Score</h4>
                            <div class="number">${result.score}%</div>
                        </div>
                    ` : ''}
                </div>

                <div class="completion-message">
                    <p>Thank you for completing the ${this.currentTest.name}!</p>
                    <p>Your answers have been recorded.</p>
                </div>
            </div>
        `;
    }

    showError(message) {
        alert(message); // Simple error handling - could be improved with a modal
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new TestApp();
});
