<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Test System</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>English Test System</h1>
            <p>Take your English proficiency test online</p>
        </header>

        <!-- Test Selection Screen -->
        <div id="test-selection" class="screen active">
            <h2>Select a Test</h2>
            <div id="tests-list" class="tests-grid">
                <!-- Tests will be loaded here -->
            </div>
        </div>

        <!-- Test Instructions Screen -->
        <div id="test-instructions" class="screen">
            <h2>Test Instructions</h2>
            <div id="instructions-content">
                <div class="instruction-box">
                    <h3>Before You Begin</h3>
                    <ul>
                        <li>This test contains multiple choice questions</li>
                        <li>Select the best answer for each question</li>
                        <li>You can navigate between questions using the Previous/Next buttons</li>
                        <li>Review your answers before submitting</li>
                        <li>Make sure you have a stable internet connection</li>
                    </ul>
                </div>
                <div id="test-info">
                    <!-- Test information will be loaded here -->
                </div>
            </div>
            <div class="button-group">
                <button id="back-to-selection" class="btn btn-secondary">Back to Test Selection</button>
                <button id="start-test" class="btn btn-primary">Start Test</button>
            </div>
        </div>

        <!-- Test Taking Screen -->
        <div id="test-taking" class="screen">
            <div class="test-header">
                <div class="test-progress">
                    <span id="current-question">1</span> of <span id="total-questions">0</span>
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                </div>
                <div class="test-timer">
                    <span id="timer">00:00</span>
                </div>
            </div>

            <div class="question-container">
                <div id="question-content">
                    <!-- Question will be loaded here -->
                </div>
            </div>

            <div class="navigation">
                <button id="prev-question" class="btn btn-secondary" disabled>Previous</button>
                <button id="review-answers" class="btn btn-outline">Review Answers</button>
                <button id="next-question" class="btn btn-primary">Next</button>
                <button id="submit-test" class="btn btn-success" style="display: none;">Submit Test</button>
            </div>
        </div>

        <!-- Review Screen -->
        <div id="review-screen" class="screen">
            <h2>Review Your Answers</h2>
            <div id="review-content">
                <!-- Review content will be loaded here -->
            </div>
            <div class="button-group">
                <button id="back-to-test" class="btn btn-secondary">Back to Test</button>
                <button id="final-submit" class="btn btn-success">Submit Final Answers</button>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="results-screen" class="screen">
            <h2>Test Results</h2>
            <div id="results-content">
                <!-- Results will be loaded here -->
            </div>
            <div class="button-group">
                <button id="take-another-test" class="btn btn-primary">Take Another Test</button>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loading" class="loading-overlay">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
