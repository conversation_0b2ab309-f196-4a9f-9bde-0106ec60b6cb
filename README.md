# English Test System

A web application built with FastAPI and Python3 for taking English proficiency tests online. The application parses test content from Word documents and presents them as interactive online exams.

## Features

- **Multiple Test Support**: Load and manage multiple English tests
- **Interactive Interface**: Modern, responsive web interface
- **Progress Tracking**: Real-time progress bar and timer
- **Answer Review**: Review and modify answers before submission
- **Question Navigation**: Navigate between questions easily
- **Mobile Responsive**: Works on desktop and mobile devices

## Test Content

The application currently includes two English tests parsed from Word documents:
- **Test 1**: 88 TOEIC-style grammar and vocabulary questions
- **Test 2**: 88 TOEIC-style grammar and vocabulary questions

## Technology Stack

- **Backend**: FastAPI (Python 3)
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Document Parsing**: python-docx
- **Data Format**: JSON
- **Server**: Uvicorn ASGI server

## Installation

1. **Clone or download the project files**

2. **Create a virtual environment**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Ensure test data is available**:
   - Make sure `test-1.docx` and `test-2.docx` are in the project directory
   - Run the parser to generate JSON data:
     ```bash
     python parse_tests.py
     ```

## Running the Application

1. **Start the server**:
   ```bash
   python main.py
   ```

2. **Open your browser** and navigate to:
   ```
   http://localhost:8000
   ```

## API Endpoints

- `GET /` - Main application page
- `GET /api/tests` - Get list of available tests
- `GET /api/test/{test_id}` - Get test information
- `GET /api/test/{test_id}/questions` - Get test questions
- `GET /api/test/{test_id}/question/{question_number}` - Get specific question
- `POST /api/test/{test_id}/submit` - Submit test answers
- `GET /health` - Health check endpoint

## File Structure

```
├── main.py                 # FastAPI application
├── models.py              # Pydantic data models
├── database.py            # Data loading and management
├── parse_tests.py         # Document parser script
├── requirements.txt       # Python dependencies
├── test-1.docx           # Test document 1
├── test-2.docx           # Test document 2
├── test1_data.json       # Parsed test 1 data
├── test2_data.json       # Parsed test 2 data
├── static/
│   ├── index.html        # Main HTML page
│   ├── style.css         # CSS styles
│   └── script.js         # JavaScript functionality
└── README.md             # This file
```

## Usage

1. **Select a Test**: Choose from available tests on the main page
2. **Read Instructions**: Review test information and instructions
3. **Take the Test**: Answer questions using the multiple-choice interface
4. **Navigate**: Use Previous/Next buttons or jump to specific questions
5. **Review Answers**: Check your answers before final submission
6. **Submit**: Submit your completed test to see results

## Development

To add new tests:

1. Place the Word document in the project directory
2. Update `parse_tests.py` to include the new document
3. Run the parser to generate JSON data
4. Update `database.py` to load the new test data

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License

This project is for educational purposes.
