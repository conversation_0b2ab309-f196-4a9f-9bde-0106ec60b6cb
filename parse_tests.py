#!/usr/bin/env python3
"""
Parse test documents and extract questions and answers
"""

from docx import Document
import re
import json

def parse_test_document(filename):
    """Parse a test document and extract questions and answers"""
    doc = Document(filename)
    questions = []
    current_question = None
    
    for para in doc.paragraphs:
        text = para.text.strip()
        if not text:
            continue
            
        # Check if this is a question number (e.g., "101.", "102.")
        question_match = re.match(r'^(\d+)\.\s+(.+)', text)
        if question_match:
            # Save previous question if exists
            if current_question:
                questions.append(current_question)
            
            # Start new question
            question_num = question_match.group(1)
            question_text = question_match.group(2)
            current_question = {
                'number': int(question_num),
                'question': question_text,
                'options': [],
                'correct_answer': None
            }
        
        # Check if this is an answer option (e.g., "(A)", "(B)", etc.)
        elif current_question and re.match(r'^\([A-D]\)', text):
            option_match = re.match(r'^\(([A-D])\)\s*(.+)', text)
            if option_match:
                option_letter = option_match.group(1)
                option_text = option_match.group(2)
                current_question['options'].append({
                    'letter': option_letter,
                    'text': option_text
                })
    
    # Don't forget the last question
    if current_question:
        questions.append(current_question)
    
    return questions

def parse_reading_passages(filename):
    """Extract reading passages from the document"""
    doc = Document(filename)
    passages = []
    
    # Look for tables which might contain reading passages
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                text = cell.text.strip()
                if len(text) > 200:  # Assume passages are longer texts
                    passages.append(text)
    
    return passages

if __name__ == "__main__":
    # Parse both test files
    test1_questions = parse_test_document("test-1.docx")
    test2_questions = parse_test_document("test-2.docx")
    
    test1_passages = parse_reading_passages("test-1.docx")
    test2_passages = parse_reading_passages("test-2.docx")
    
    print(f"Test 1: Found {len(test1_questions)} questions and {len(test1_passages)} passages")
    print(f"Test 2: Found {len(test2_questions)} questions and {len(test2_passages)} passages")
    
    # Show first few questions from each test
    print("\nFirst 3 questions from Test 1:")
    for q in test1_questions[:3]:
        print(f"Q{q['number']}: {q['question']}")
        for opt in q['options']:
            print(f"  ({opt['letter']}) {opt['text']}")
        print()
    
    print("\nFirst 3 questions from Test 2:")
    for q in test2_questions[:3]:
        print(f"Q{q['number']}: {q['question']}")
        for opt in q['options']:
            print(f"  ({opt['letter']}) {opt['text']}")
        print()
    
    # Save parsed data to JSON files
    with open('test1_data.json', 'w', encoding='utf-8') as f:
        json.dump({
            'questions': test1_questions,
            'passages': test1_passages
        }, f, indent=2, ensure_ascii=False)
    
    with open('test2_data.json', 'w', encoding='utf-8') as f:
        json.dump({
            'questions': test2_questions,
            'passages': test2_passages
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\nData saved to test1_data.json and test2_data.json")
