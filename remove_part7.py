#!/usr/bin/env python3
"""
Remove Part 7 from the English test application
"""

import json

def remove_part7_from_tests():
    """Remove all Part 7 questions and passages from test data"""
    
    for test_file in ['test-1_enhanced.json', 'test-2_enhanced.json']:
        try:
            print(f"Removing Part 7 from {test_file}...")
            
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Remove all Part 7 questions
            original_count = len(data['questions'])
            data['questions'] = [q for q in data['questions'] if q.get('question_type') != 'part7']
            part7_removed = original_count - len(data['questions'])
            
            # Remove all passages (since they were only for Part 7)
            passages_removed = len(data.get('passages', []))
            data['passages'] = []
            
            # Save updated data
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Count remaining questions by type
            part5_count = len([q for q in data['questions'] if q.get('question_type') == 'part5'])
            part6_count = len([q for q in data['questions'] if q.get('question_type') == 'part6'])
            
            print(f"✅ Updated {test_file}")
            print(f"   Removed {part7_removed} Part 7 questions")
            print(f"   Removed {passages_removed} passages")
            print(f"   Remaining questions:")
            print(f"     Part 5: {part5_count} questions")
            print(f"     Part 6: {part6_count} questions")
            print(f"     Total: {len(data['questions'])} questions")
            
        except Exception as e:
            print(f"❌ Error updating {test_file}: {e}")

def update_frontend_to_remove_part7():
    """Update the frontend JavaScript to remove Part 7 handling"""
    
    print("Updating frontend to remove Part 7 support...")
    
    # The frontend will automatically handle this since it checks question types
    # and only renders what's available in the data
    print("✅ Frontend will automatically adapt to Part 5 and Part 6 only")

if __name__ == "__main__":
    print("🗑️ Removing Part 7 from English test application...")
    remove_part7_from_tests()
    update_frontend_to_remove_part7()
    print("✅ Part 7 removed successfully! Test now contains only Part 5 and Part 6.")
