"""
Pydantic models for the English Test API
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from enum import Enum

class QuestionType(str, Enum):
    PART5 = "part5"  # Single fill-in-the-blank
    PART6 = "part6"  # Paragraph with multiple blanks
    PART7 = "part7"  # Reading comprehension with passage

class QuestionOption(BaseModel):
    letter: str
    text: str

class Question(BaseModel):
    number: int
    question: str
    options: List[QuestionOption]
    correct_answer: Optional[str] = None
    question_type: QuestionType = QuestionType.PART5
    passage_id: Optional[str] = None  # For Part 7 questions
    paragraph_text: Optional[str] = None  # For Part 6 questions
    blank_positions: Optional[List[int]] = None  # For Part 6 multiple blanks

class Passage(BaseModel):
    id: str
    title: Optional[str] = None
    content: str
    question_numbers: List[int] = []  # Questions that refer to this passage

class Test(BaseModel):
    id: str
    name: str
    description: str
    questions: List[Question]
    passages: List[Passage] = []

class UserAnswer(BaseModel):
    question_number: int
    selected_answer: str

class TestSubmission(BaseModel):
    test_id: str
    answers: List[UserAnswer]

class TestResult(BaseModel):
    test_id: str
    total_questions: int
    answered_questions: int
    score: Optional[int] = None
    percentage: Optional[float] = None
    answers: List[UserAnswer]
    time_taken: Optional[int] = None  # in seconds

class TestInfo(BaseModel):
    id: str
    name: str
    description: str
    total_questions: int
    estimated_time: int  # in minutes
