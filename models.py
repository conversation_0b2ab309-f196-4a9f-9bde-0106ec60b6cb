"""
Pydantic models for the English Test API
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any

class QuestionOption(BaseModel):
    letter: str
    text: str

class Question(BaseModel):
    number: int
    question: str
    options: List[QuestionOption]
    correct_answer: Optional[str] = None

class Test(BaseModel):
    id: str
    name: str
    description: str
    questions: List[Question]
    passages: List[str] = []

class UserAnswer(BaseModel):
    question_number: int
    selected_answer: str

class TestSubmission(BaseModel):
    test_id: str
    answers: List[UserAnswer]

class TestResult(BaseModel):
    test_id: str
    total_questions: int
    answered_questions: int
    score: Optional[int] = None
    percentage: Optional[float] = None
    answers: List[UserAnswer]
    time_taken: Optional[int] = None  # in seconds

class TestInfo(BaseModel):
    id: str
    name: str
    description: str
    total_questions: int
    estimated_time: int  # in minutes
