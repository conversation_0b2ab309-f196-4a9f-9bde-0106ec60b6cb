#!/usr/bin/env python3
"""
Add <PERSON>'s Place questionnaire and other missing authentic TOEIC Part 7 formats
"""

import json

def create_authentic_part7_passages():
    """Create authentic TOEIC-style Part 7 passages including <PERSON>'s Place questionnaire"""
    
    passages = [
        {
            'id': 'passage_1',
            'title': 'Job Advertisement - Maintenance Supervisor',
            'content': '''MAINTENANCE SUPERVISOR POSITION AVAILABLE

We are seeking a qualified maintenance supervisor for our 200-unit apartment complex located in downtown Springfield. The successful candidate will be responsible for the general upkeep, repair, and maintenance of the building and grounds.

RESPONSIBILITIES:
• Supervise daily maintenance operations
• Coordinate repair work with contractors
• Maintain heating, plumbing, and electrical systems
• Ensure building safety and cleanliness
• Handle tenant maintenance requests

REQUIREMENTS:
• Minimum 3 years experience in building maintenance
• Knowledge of HVAC, plumbing, and electrical systems
• Strong leadership and communication skills
• Ability to work independently and handle emergencies
• Valid driver's license

WE OFFER:
• Competitive salary: $45,000-$55,000 annually
• Free 2-bedroom apartment on-site
• Comprehensive health insurance
• Paid vacation and sick leave
• Professional development opportunities

This is an excellent opportunity for an experienced maintenance professional looking to advance their career in property management. The position requires someone who can work well under pressure and manage multiple priorities.

Interested candidates should submit their resume and three professional references to the building management office by March 31st.''',
            'question_numbers': [153, 154]
        },
        {
            'id': 'passage_2',
            'title': "Sophie's Place Restaurant Questionnaire",
            'content': '''Sophie's Place
Questionnaire

Dear Guests,

Your continuous support and patronage is our greatest source of comfort. Your heartfelt comments are our compelling drive to provide better service for you. Please kindly complete this questionnaire to let us know your thoughts. Thank you very much.

Name: John Willis
Telephone: 755-2563
Date of visit: Nov. 28, 2006
Time of visit: 6:30 p.m.

┌─────────────────────┬──────┬──────┬──────┬───────────┐
│ Quality of Food     │ Good │ Fair │ Poor │ Very Poor │
├─────────────────────┼──────┼──────┼──────┼───────────┤
│ Taste               │ (✓)  │ ( )  │ ( )  │ ( )       │
│ Variety             │ ( )  │ ( )  │ (✓)  │ ( )       │
│ Price               │ (✓)  │ ( )  │ ( )  │ ( )       │
└─────────────────────┴──────┴──────┴──────┴───────────┘

┌─────────────────────┬──────┬──────┬──────┬───────────┐
│ Quality of Service  │ Good │ Fair │ Poor │ Very Poor │
├─────────────────────┼──────┼──────┼──────┼───────────┤
│ Efficiency of Service│ ( )  │ ( )  │ (✓)  │ ( )       │
│ Staff Courtesy      │ ( )  │ (✓)  │ ( )  │ ( )       │
│ Cleanliness         │ ( )  │ (✓)  │ ( )  │ ( )       │
└─────────────────────┴──────┴──────┴──────┴───────────┘

Further comments:

We had to wait over an hour for our food to arrive. The food was very tasty, but I think the place needs more people to wait on customers.''',
            'question_numbers': [155, 156, 157]
        },
        {
            'id': 'passage_3',
            'title': 'Office Renovation Schedule',
            'content': '''OFFICE BUILDING RENOVATION SCHEDULE

TO: All Employees
FROM: Facilities Management Department
DATE: March 1, 2024
RE: Upcoming Office Renovations

We are pleased to announce that our headquarters building will undergo comprehensive renovations to improve working conditions and modernize our facilities.

RENOVATION SCHEDULE:
┌─────────────────┬──────────────────┬─────────────────────────┐
│ Floor           │ Dates            │ Departments Affected    │
├─────────────────┼──────────────────┼─────────────────────────┤
│ Ground Floor    │ March 15-25      │ Accounting, HR          │
│ Second Floor    │ April 1-10       │ Marketing, Sales        │
│ Third Floor     │ April 15-25      │ Operations, IT          │
│ Fourth Floor    │ May 1-10         │ Executive Offices       │
└─────────────────┴──────────────────┴─────────────────────────┘

TEMPORARY RELOCATIONS:
During renovation periods, affected employees will be relocated to available spaces on other floors. We urge all staff to be flexible and patient during this transition.

IMPROVEMENTS INCLUDE:
• New energy-efficient lighting systems
• Updated HVAC for better climate control
• Fresh paint and modern carpeting
• Ergonomic furniture and workstations
• Enhanced network infrastructure

WHAT TO EXPECT:
• Some noise and dust during work hours
• Temporary disruption to normal routines
• Limited access to certain areas
• Possible parking restrictions

We expect minimal impact on daily operations. However, we ask for your cooperation and understanding during this improvement project.

For questions or concerns, please contact the Facilities Department at extension 2500.''',
            'question_numbers': [158, 159, 160, 161]
        },
        {
            'id': 'passage_4',
            'title': 'Store Return Policy',
            'content': '''CUSTOMER RETURN POLICY

All merchandise purchased at our store may be returned for a full refund within 30 days of purchase, subject to the following conditions:

GENERAL REQUIREMENTS:
• Items must be in original, unused condition
• Original packaging must be intact
• Proof of purchase (receipt) must be provided
• Items must not show signs of wear or damage

SPECIAL CONDITIONS:
• Food items from fresh counters: 24-hour return limit
• Electronics, software, CDs, DVDs: Must be unopened
• Personal items (undergarments, swimwear): No returns
• Custom or personalized items: No returns

IDENTIFICATION REQUIREMENTS:
• Returns over $100: Photo ID required
• Gift returns: Original gift receipt or purchaser ID needed
• Credit card purchases: May require original payment card

REFUND PROCESSING:
• Under $100: Immediate cash refund or store credit
• Over $100: Check mailed within 3-5 business days
• Gift returns: Store credit at current selling price

We reserve the right to refuse returns that do not meet these conditions. Our goal is to ensure customer satisfaction while maintaining fair business practices.

For questions about returns, please speak with a customer service representative.''',
            'question_numbers': [162, 163]
        },
        {
            'id': 'passage_5',
            'title': 'Sale Advertisement with Pricing Table',
            'content': '''ANNUAL WAREHOUSE CLEARANCE SALE!

EVERYTHING MUST GO!
UP TO 70% OFF ORIGINAL PRICES

SALE DATES: March 1-31, 2024
STORE HOURS: 9:00 AM - 9:00 PM (Monday-Saturday)
             10:00 AM - 6:00 PM (Sunday)

DISCOUNT TABLE BY DEPARTMENT:
┌─────────────────────┬─────────────────┬─────────────────────┐
│ Department          │ Discount Range  │ Special Offers      │
├─────────────────────┼─────────────────┼─────────────────────┤
│ Winter Clothing     │ 50-70% off      │ Buy 2, Get 1 FREE   │
│ Electronics         │ 30-50% off      │ Extended warranty   │
│ Home & Garden       │ 40-60% off      │ Free delivery       │
│ Books & Media       │ 25-40% off      │ 3 for 2 deal        │
│ Sporting Goods      │ 35-55% off      │ Price matching      │
└─────────────────────┴─────────────────┴─────────────────────┘

PREMIER CLUB MEMBERSHIP BENEFITS:
• Exclusive early access to sales (starts Feb 28)
• Additional 10% off all sale prices
• Free shipping on online orders
• Birthday month special offers
• Extended return policy (60 days)

CUSTOMER SERVICES:
• Personal shopping assistance available
• Layaway program for large purchases
• Price matching on identical items
• Extended warranty options

Visit our main location at 123 Commerce Drive or shop online at www.clearancewarehouse.com

*Some restrictions apply. See store associate for complete details.''',
            'question_numbers': [164, 165]
        },
        {
            'id': 'passage_6',
            'title': 'Package Delivery Form',
            'content': '''DELIVERY CONFIRMATION NOTICE

TRACKING NUMBER: PKG789456123
DELIVERY DATE: March 20, 2024, 2:30 PM

┌─────────────────────────────────────────────────────────────┐
│                    RECIPIENT INFORMATION                    │
├─────────────────────────────────────────────────────────────┤
│ Name:     Mr. Mitchell                                      │
│ Address:  456 Oak Street, Apartment 2B                     │
│           Springfield, IL 62701                            │
│ Phone:    (*************                                   │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                     SENDER INFORMATION                     │
├─────────────────────────────────────────────────────────────┤
│ Name:     Mr. Lee - ABC Electronics                        │
│ Address:  789 Industrial Boulevard                         │
│           Chicago, IL 60601                               │
│ Phone:    (*************                                  │
└─────────────────────────────────────────────────────────────┘

PACKAGE DETAILS:
┌─────────────────────┬───────────────────────────────────────┐
│ Number of Items     │ 2                                     │
│ Contents           │ • Dell Laptop Computer (Model XPS-15) │
│                    │ • Wireless Bluetooth Mouse            │
│ Package Weight     │ 8.5 lbs                              │
│ Declared Value     │ $1,250.00                            │
│ Shipping Cost      │ $15.99                               │
└─────────────────────┴───────────────────────────────────────┘

DELIVERY INSTRUCTIONS:
• Signature required upon delivery
• Adult signature (18+) required
• If recipient unavailable, package held at local post office
• Maximum hold period: 30 calendar days
• After 30 days, package returned to sender

DELIVERY STATUS: ✓ COMPLETED SUCCESSFULLY
Delivered to: Mr. Mitchell (signature on file)
Delivery Location: Front door of apartment

For questions about this delivery, contact: 1-800-555-SHIP (7447)''',
            'question_numbers': [166, 167, 168]
        },
        {
            'id': 'passage_7',
            'title': 'Business Letter and Invoice',
            'content': '''CUSTOMER SERVICE RESPONSE LETTER

Bridges Publishing Company
123 Publisher Lane
New York, NY 10001

October 19, 2024

Ms. Angelina Parker
456 Subscriber Street
Boston, MA 02101

Dear Ms. Parker,

Thank you for your letter dated October 18 regarding the missing issues of your magazine subscription. We sincerely apologize for this inconvenience.

After investigating your account, we found that issues from September and October were indeed not delivered due to a postal service error in your area. We have taken immediate steps to resolve this matter.

COMPENSATION DETAILS:
┌─────────────────────────────────────────────────────────────┐
│ Missing Issues: September & October 2024                   │
│ Compensation: 25% discount on next year's subscription     │
│ Additional: 2 complimentary issues of our sister magazine  │
│ Processing: Discount applied automatically to your account │
└─────────────────────────────────────────────────────────────┘

We value your loyalty as a subscriber and want to ensure your complete satisfaction. The enclosed check covers the cost of the missing magazines, and we have also included information about our other publications that you might enjoy at no additional cost.

Please contact our customer service department at (800) 555-MAGS if you have any further concerns.

Sincerely,
Robert Bridges
Customer Service Manager

Enclosure: Check for $12.50''',
            'question_numbers': [169, 170, 171, 172]
        },
        {
            'id': 'passage_8',
            'title': 'Corporate News Report',
            'content': '''BUSINESS NEWS REPORT

GEM COMPUTERS SELLS DESKTOP DIVISION

NEW YORK - In a surprising move announced yesterday, GEM Computers Inc. has agreed to sell its struggling desktop computer division to Electronica Corp for $45 million. The transaction is expected to be completed by early next year.

FINANCIAL IMPACT:
┌─────────────────────┬─────────────────────────────────────┐
│ Sale Price          │ $45 million                         │
│ Division Revenue    │ $12 million annually (declining)    │
│ Employees Affected  │ 150 (will transfer to Electronica) │
│ GEM Stock Response  │ +8% in after-hours trading         │
└─────────────────────┴─────────────────────────────────────┘

The desktop division has been underperforming for the past three years as consumer demand shifted toward laptops and mobile devices. GEM CEO Sarah Johnson stated, "This strategic decision allows us to focus our resources on our more profitable laptop and server divisions."

Market analysts view the sale positively, with many suggesting that GEM got a favorable deal considering the division's declining performance. Electronica, known for its budget electronics, plans to integrate the desktop operations into its existing manufacturing facilities.

The sale is subject to regulatory approval and is expected to close in the first quarter of next year. Both companies declined to comment on specific terms beyond the purchase price.''',
            'question_numbers': [173, 174, 175, 176]
        }
    ]
    
    return passages

def update_with_sophies_questionnaire():
    """Update test files with Sophie's Place questionnaire and other authentic formats"""
    
    authentic_passages = create_authentic_part7_passages()
    
    for test_file in ['test-1_enhanced.json', 'test-2_enhanced.json']:
        try:
            print(f"Updating {test_file} with Sophie's Place questionnaire...")
            
            with open(test_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Replace passages with authentic versions
            data['passages'] = authentic_passages
            
            # Update Part 7 question passage assignments
            for question in data['questions']:
                if question.get('question_type') == 'part7':
                    qnum = question['number']
                    
                    if 153 <= qnum <= 154:
                        question['passage_id'] = 'passage_1'  # Job Advertisement
                    elif 155 <= qnum <= 157:
                        question['passage_id'] = 'passage_2'  # Sophie's Place Questionnaire
                    elif 158 <= qnum <= 161:
                        question['passage_id'] = 'passage_3'  # Office Renovation
                    elif 162 <= qnum <= 163:
                        question['passage_id'] = 'passage_4'  # Store Return Policy
                    elif 164 <= qnum <= 165:
                        question['passage_id'] = 'passage_5'  # Sale Advertisement
                    elif 166 <= qnum <= 168:
                        question['passage_id'] = 'passage_6'  # Package Delivery
                    elif 169 <= qnum <= 172:
                        question['passage_id'] = 'passage_7'  # Business Letter
                    elif 173 <= qnum <= 176:
                        question['passage_id'] = 'passage_8'  # Corporate News
                    else:
                        # For remaining questions, cycle through passages
                        passage_num = ((qnum - 177) % 8) + 1
                        question['passage_id'] = f'passage_{passage_num}'
            
            # Save updated data
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Updated {test_file}")
            print(f"   Added Sophie's Place questionnaire as passage_2")
            print(f"   Questions 155-157 now linked to restaurant questionnaire")
            
            # Show passage distribution
            part7_questions = [q for q in data['questions'] if q.get('question_type') == 'part7']
            passage_counts = {}
            for q in part7_questions:
                pid = q.get('passage_id', 'unknown')
                passage_counts[pid] = passage_counts.get(pid, 0) + 1
            
            print(f"   Passage distribution: {passage_counts}")
            
        except Exception as e:
            print(f"❌ Error updating {test_file}: {e}")

if __name__ == "__main__":
    print("🍽️ Adding Sophie's Place questionnaire and other authentic TOEIC formats...")
    update_with_sophies_questionnaire()
    print("✅ Sophie's Place questionnaire added successfully!")
